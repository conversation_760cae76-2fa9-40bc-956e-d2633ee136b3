import json
import yaml
import requests
from urllib.parse import urlparse
import time


def parse(source):
    """
    Process a Swagger file (JSON or YAML) and extract standalone endpoints.

    Args:
        source (str): File path or URL to the Swagger specification

    Returns:
        list: List of standalone endpoint objects
    """
    start = time.time()
    last_time = start
    # Step 1: Get content from file or URL
    content = _get_content(source)
    print(f"Time to get content: {time.time() - last_time}")
    last_time = time.time()

    # Step 2: Identify format and parse content
    swagger_data = _parse_content(content)
    print(f"Time to parse content: {time.time() - last_time}")
    last_time = time.time()

    # Step 3: Extract endpoints
    endpoints = _extract_endpoints(swagger_data)
    print(f"Time to extract endpoints: {time.time() - last_time}")
    last_time = time.time()
    print(f"Total time: {last_time - start}")

    return endpoints


def _get_content(source):
    """
    Get content from either a file path or URL.

    Args:
        source (str): File path or URL

    Returns:
        str: Content of the swagger file
    """
    parsed_url = urlparse(source)

    # Check if source is a URL
    if parsed_url.scheme in ["http", "https"]:
        response = requests.get(source)
        response.raise_for_status()  # Raise exception for 4XX/5XX responses
        return response.text
    else:
        # Treat as file path
        with open(source, "r", encoding="utf-8") as file:
            return file.read()


def _parse_content(content):
    """
    Parse content and convert to Python dict.
    Try JSON first, then YAML.

    Args:
        content (str): Swagger file content

    Returns:
        dict: Parsed Swagger data
    """
    try:
        # Try parsing as JSON
        return json.loads(content)
    except json.JSONDecodeError:
        try:
            # Try parsing as YAML
            return yaml.safe_load(content)
        except yaml.YAMLError:
            raise ValueError("Content is neither valid JSON nor YAML")


def _extract_endpoints(swagger_data):
    """
    Extract standalone endpoints from parsed Swagger data.

    Args:
        swagger_data (dict): Parsed Swagger specification

    Returns:
        list: List of standalone endpoint objects
    """
    endpoints = []

    # Get base path from Swagger data
    base_path = swagger_data.get("basePath", "")

    # Get global produces, consumes, parameters if available
    global_produces = swagger_data.get("produces", [])
    global_consumes = swagger_data.get("consumes", [])

    # Get common definitions/schemas
    definitions = swagger_data.get("definitions", {})
    schemas = (
        swagger_data.get("components", {}).get("schemas", {})
        if "components" in swagger_data
        else {}
    )

    # Handle OpenAPI v2 (Swagger) and OpenAPI v3 formats
    paths = swagger_data.get("paths", {})

    # Process each path
    for path, path_item in paths.items():
        # Handle path-level parameters
        path_parameters = path_item.get("parameters", [])

        # Process each HTTP method for the path
        for method, operation in path_item.items():
            # Skip non-operation keys
            if method in ["parameters", "$ref"]:
                continue

            # Create standalone endpoint
            endpoint = {
                "path": base_path + path,
                "method": method.upper(),
                "operation_id": operation.get(
                    "operationId", f"{method}_{path}".replace("/", "_")
                ),
                "summary": operation.get("summary", ""),
                "description": operation.get("description", ""),
                "parameters": path_parameters + operation.get("parameters", []),
                "responses": operation.get("responses", {}),
                "consumes": operation.get("consumes", global_consumes),
                "produces": operation.get("produces", global_produces),
                "tags": operation.get("tags", []),
            }

            # Handle OpenAPI v3 requestBody if present
            if "requestBody" in operation:
                endpoint["request_body"] = operation["requestBody"]

            # Include security requirements if present
            if "security" in operation:
                endpoint["security"] = operation["security"]
            elif "security" in swagger_data:
                endpoint["security"] = swagger_data["security"]

            endpoints.append(endpoint)

    return endpoints
