#!/usr/bin/env python3
import json
import jsonref


class SwaggerSpecRefsResolver:
    """
    Class responsible for resolving references in OpenAPI specifications.
    Handles both JSON and YAML formats and generates individual endpoint specifications.
    """

    def __init__(self):
        """
        Initialize the SwaggerSpecRefsResolver.
        """
        pass

    def resolve_references(self, swagger_dict, base_uri=None):
        """
        Resolve all references in the OpenAPI specification dictionary.

        Args:
            swagger_dict (dict): The OpenAPI specification dictionary
            base_uri (str, optional): The base URI for resolving references

        Returns:
            dict: The specification with all references resolved

        Raises:
            Exception: If there's an error resolving references
        """
        try:
            # Convert dict to JSON string
            json_content = json.dumps(swagger_dict)

            # Resolve references
            resolved_dict = jsonref.loads(
                json_content, base_uri=base_uri, jsonschema=True
            )
            return resolved_dict
        except Exception as e:
            raise Exception(f"Error resolving references: {str(e)}")

    def _detect_openapi_version(self, swagger_dict):
        """
        Detect the OpenAPI specification version from the swagger dictionary.

        Args:
            swagger_dict (dict): The OpenAPI specification dictionary

        Returns:
            int: The detected OpenAPI version (1, 2, or 3)
        """
        if "swagger" in swagger_dict:
            version = swagger_dict["swagger"]
            if version.startswith("1."):
                return 1
            elif version.startswith("2."):
                return 2
        elif "openapi" in swagger_dict:
            version = swagger_dict["openapi"]
            if version.startswith("3."):
                return 3

        # Default to version 2 if we can't determine
        print("Warning: Could not determine OpenAPI version, defaulting to 2.0")
        return 2

    def _get_definitions(self, swagger_dict, openapi_version):
        """
        Get the schema definitions based on the OpenAPI version.

        Args:
            swagger_dict (dict): The OpenAPI specification dictionary
            openapi_version (int): The OpenAPI version

        Returns:
            dict: The schema definitions
        """
        if openapi_version <= 2:
            return swagger_dict.get("definitions", {})
        else:  # OpenAPI 3
            return swagger_dict.get("components", {}).get("schemas", {})

    def _find_referenced_schemas(
        self, obj, collected_schemas, definitions, openapi_version
    ):
        """
        Recursively find referenced schemas in an object.

        Args:
            obj: The object to search for references
            collected_schemas (dict): Dictionary to collect found schemas
            definitions (dict): The schema definitions
            openapi_version (int): The OpenAPI version
        """
        if isinstance(obj, dict):
            # Process each key-value pair
            for key, value in obj.items():
                # If this is a schema reference
                if key == "$ref" and isinstance(value, str):
                    ref_parts = value.split("/")
                    if len(ref_parts) > 2:
                        # Handle different reference formats based on version
                        if openapi_version <= 2:
                            if "definitions" in ref_parts:
                                schema_name = ref_parts[-1]
                                if schema_name in definitions:
                                    collected_schemas[schema_name] = definitions[
                                        schema_name
                                    ]
                                    # Recursively check this schema for more references
                                    self._find_referenced_schemas(
                                        definitions[schema_name],
                                        collected_schemas,
                                        definitions,
                                        openapi_version,
                                    )
                        else:  # OpenAPI 3
                            if "components" in ref_parts and "schemas" in ref_parts:
                                schema_name = ref_parts[-1]
                                if schema_name in definitions:
                                    collected_schemas[schema_name] = definitions[
                                        schema_name
                                    ]
                                    # Recursively check this schema for more references
                                    self._find_referenced_schemas(
                                        definitions[schema_name],
                                        collected_schemas,
                                        definitions,
                                        openapi_version,
                                    )
                else:
                    # Recursively process nested objects
                    self._find_referenced_schemas(
                        value, collected_schemas, definitions, openapi_version
                    )
        elif isinstance(obj, list):
            # Process each item in the list
            for item in obj:
                self._find_referenced_schemas(
                    item, collected_schemas, definitions, openapi_version
                )

    def _create_endpoint_definition(
        self, swagger_dict, path, method, operation, required_schemas, openapi_version
    ):
        """
        Create a minimal endpoint definition with only what's needed.

        Args:
            swagger_dict (dict): The OpenAPI specification dictionary
            path (str): The endpoint path
            method (str): The HTTP method
            operation (dict): The operation details
            required_schemas (dict): The required schemas for this endpoint
            openapi_version (int): The OpenAPI version

        Returns:
            dict: The endpoint definition
        """
        # Create base endpoint definition
        endpoint_def = {
            "info": {
                "title": f"{method.upper()} {path}",
                "description": operation.get("description", ""),
                "version": swagger_dict.get("info", {}).get("version", "1.0"),
            },
            "paths": {path: {method: operation}},
        }

        # Add version-specific fields
        if openapi_version <= 2:
            endpoint_def["swagger"] = swagger_dict.get("swagger", "2.0")
            endpoint_def["basePath"] = swagger_dict.get("basePath", "")
            endpoint_def["schemes"] = swagger_dict.get("schemes", [])
            endpoint_def["consumes"] = operation.get(
                "consumes", swagger_dict.get("consumes", [])
            )
            endpoint_def["produces"] = operation.get(
                "produces", swagger_dict.get("produces", [])
            )

            # Add only the required definitions if they exist
            if required_schemas:
                endpoint_def["definitions"] = required_schemas
        else:  # OpenAPI 3
            endpoint_def["openapi"] = swagger_dict.get("openapi", "3.0.0")
            endpoint_def["servers"] = swagger_dict.get("servers", [])

            # Add only the required schemas if they exist
            if required_schemas:
                endpoint_def["components"] = {"schemas": required_schemas}

        return endpoint_def

    def generate_endpoint_specs(self, swagger_dict):
        """
        Generate individual specifications for each endpoint.

        Args:
            swagger_dict (dict): The resolved OpenAPI specification dictionary

        Returns:
            list: A list of dictionaries containing endpoint specifications
        """
        openapi_version = self._detect_openapi_version(swagger_dict)
        definitions = self._get_definitions(swagger_dict, openapi_version)
        paths = swagger_dict.get("paths", {})
        endpoint_specs = []

        for path, path_item in paths.items():
            for method, operation in path_item.items():
                if method.lower() in [
                    "get",
                    "post",
                    "put",
                    "delete",
                    "patch",
                    "options",
                    "head",
                ]:
                    # Extract only the schemas/components needed for this endpoint
                    required_schemas = {}

                    # Find all referenced schemas in this operation
                    self._find_referenced_schemas(
                        operation, required_schemas, definitions, openapi_version
                    )

                    # Create a minimal endpoint definition
                    endpoint_def = self._create_endpoint_definition(
                        swagger_dict,
                        path,
                        method,
                        operation,
                        required_schemas,
                        openapi_version,
                    )

                    endpoint_specs.append(
                        {"path": path, "method": method, "spec": endpoint_def}
                    )

        return json.loads(json.dumps(endpoint_specs, default=str))
